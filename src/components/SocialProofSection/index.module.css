/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

:root {
  --primary: #4A6FFF;
  --primary-dark: #3A5BDF;
  --secondary: #FF6B6B;
  --dark: #333333;
  --light: #FFFFFF;
  --gray: #F5F7FA;
  --text: #333333;
  --text-light: #555555;
}

.socialProofSection {
  padding: 4rem 0;
  background-color: #f1f3f3;
}

.sectionTitle {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.metricsContainer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin: 3rem auto;
  max-width: 1000px;
}

.metricCard {
  background-color: var(--light);
  border-radius: 10px;
  padding: 2rem 1rem;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.metricCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.metricIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.metricValue {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.metricLabel {
  font-size: 1.1rem;
  color: var(--text-light);
}

.companiesContainer {
  margin-top: 4rem;
  text-align: center;
}

.companiesTitle {
  font-size: 1.3rem;
  color: var(--text-light);
  margin-bottom: 2rem;
}

.companyGrid {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin: 0 auto;
  max-width: 1000px;
}

/* Product Hunt Badges Styling */
.productHuntContainer {
  margin: 2rem auto 3rem;
  text-align: center;
  /* max-width: 800px; */
  padding: 1.5rem;
  /* background-color: rgba(255, 255, 255, 0.7); */
  /* border-radius: 12px; */
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); */
}

.badgesTitle {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.badgesWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  gap: 1.5rem;
}

.badgeLink {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: block;
  border-radius: 8px;
  overflow: hidden;
}

.badgeLink:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.badgeImage {
  height: 54px;
  display: block;
}

.companyItem {
  padding: 0.8rem 1.5rem;
  background-color: white;
  border-radius: 6px;
  font-weight: 600;
  color: var(--primary);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s, color 0.3s;
  font-size: 1.1rem;
}

.companyItem:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(74, 111, 255, 0.15);
  color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 992px) {
  .metricsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .metricsContainer {
    grid-template-columns: 1fr;
  }

  .companyGrid {
    gap: 1rem;
  }

  .companyItem {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
  }
}
